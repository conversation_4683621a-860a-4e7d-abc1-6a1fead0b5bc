import { expressjwt } from "express-jwt";

export const requireSignIn = expressjwt({
  secret: `${process.env.JWT_SECRET}`,
  algorithms: ["HS256"],
  requestProperty: "user",
});

// middlewares/authMiddleware.js
export const isAdmin = (req, res, next) => {
  if (req.user?.role !== "admin") {
    return res.status(403).send({
      success: false,
      message: "Access Denied. Admins only.",
    });
  }
  next();
};

export const isSeller = (req, res, next) => {
  if (req.user?.role !== "seller") {
    return res.status(403).send({
      success: false,
      message: "Access Denied. Sellers only.",
    });
  }
  next();
};

// middlewares/authMiddleware.js

export const authorizeRoles = (...allowedRoles) => {
  return (req, res, next) => {
    const userRole = req.user?.role;

    if (!allowedRoles.includes(userRole)) {
      return res.status(403).send({
        success: false,
        message: "Access denied. Unauthorized role.",
      });
    }

    next();
  };
};
